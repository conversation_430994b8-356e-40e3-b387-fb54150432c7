# Tech Context: BudApp

## Technology Stack

### Backend
- **Runtime**: Node.js 22 LTS with TypeScript (pure ESM)
- **API**: Fastify 5 + GraphQL (Mercurius), code-first schema approach
- **Database**: Supabase (PostgreSQL) with Drizzle ORM and Row-Level Security
- **Authentication**: Custom JWT + OAuth 2.0 (Google, Apple)
- **Service Layer**: Modular services with Zod validation and comprehensive error handling
  - **Accounts Service**: Complete CRUD operations with authentication and soft delete
  - **Auth Service**: User registration, login, token management
  - **Categories Service**: Hierarchical categories with icon/color selection
  - **Transactions Service**: Complete CRUD with double-entry accounting, filtering, and pagination
- **GraphQL Schema**: Modular type definitions with Account, Auth, Category, and Transaction types
- **Double-Entry Accounting**: Comprehensive transaction validation ensuring debits equal credits
- **Testing**: Vitest with @vitest/coverage-istanbul, MSW for mocking, **543/543 tests passing**
- **Tools**: Pino logging, BullMQ + Redis, Resend email, Sentry monitoring

### Mobile
- **Framework**: Flutter SDK with <PERSON><PERSON>, Material 3 + Cupertino components
- **Navigation**: go_router with shell routes, MainNavigationShell for persistent bottom nav
- **State Management**: Riverpod with riverpod_annotation (accounts and categories integration complete, transactions in progress)
- **Data**: graphql_flutter for GraphQL client, flutter_secure_storage for tokens
- **UI**: Complete screens (Home, Accounts, Categories, Reports, Profile) with static financial data
- **Transaction Management**: Advanced transaction list with date grouping, filtering, search, and real-time updates
- **Code Quality & Architecture**: Complete mobile app refactoring with modern patterns
  - **Widget Architecture**: Private widgets with single responsibilities and `const` constructors
  - **Immutable Data Models**: All models converted to `@freezed` (Category, User, AuthPayload, etc.)
  - **State Management**: `@freezed` state classes (AccountsState, AuthState) with automatic code generation
  - **Performance Optimization**: Const constructors and optimized widget rebuilds throughout
- **Design System**: Centralized design tokens with `design_tokens.dart` as single source of truth
  - **Theme Integration**: `app_theme.dart` consumes all values from design tokens
  - **Semantic Colors**: Comprehensive color system with `AppColors.primary`, `AppColors.onPrimary`, etc.
  - **Component Consistency**: All UI components use centralized design tokens (61+ hardcoded colors eliminated)
  - **Financial Semantics**: `AppColors.success`, `AppColors.error`, `AppColors.info` for transaction types
- **Environment Configuration**: Flexible API endpoint configuration using `--dart-define`
  - **Environment Class**: `lib/config/environment.dart` with compile-time configuration
  - **Build Scripts**: Environment-specific builds (dev, local, staging, production)
  - **Team Collaboration**: Different developers can use different local network configurations
- **Account Management**: Static UI ready for API integration with real account data
- **Categories Management**: Complete integration with real API data and icon/color selection
- **Transaction Models**: Complete Freezed models with GraphQL service layer and Riverpod providers
- **Code Generation**: `freezed`, `json_serializable`, `riverpod_annotation` for automatic boilerplate
- **Testing**: flutter_test (unit/widget), integration_test (E2E)

### Build System
- **Monorepo**: Turborepo with pnpm workspaces (apps-only structure)
- **Code Quality**: Biome.js for unified linting/formatting (0 errors/warnings)
- **CI/CD**: GitHub Actions with Docker, security scanning, automated testing

## Development Setup

### Repository Structure
```
apps/
├── api/                 # Fastify + GraphQL API
│   ├── src/database/    # Schema, migrations, seeding
│   ├── src/services/    # Business logic (accounts, auth, categories, transactions)
│   ├── src/graphql/     # Schema definitions and resolvers
│   ├── scripts/         # Schema generation
│   └── generated/       # Generated GraphQL schema
└── mobile/              # Flutter app
    ├── graphql/         # GraphQL operations
    ├── lib/config/      # App configuration
    │   ├── environment.dart      # Environment configuration
    │   ├── design_tokens.dart    # Centralized design tokens
    │   └── app_theme.dart        # Theme using design tokens
    ├── lib/models/      # Data models (account, category, transaction)
    ├── lib/services/    # GraphQL services (accounts, categories, transactions)
    ├── lib/providers/   # Riverpod providers (accounts, categories, transactions)
    ├── lib/screens/     # UI screens (home, accounts, categories, transactions)
    ├── scripts/         # Build scripts
    │   ├── build-dev.sh          # Development builds
    │   ├── build-local.sh        # Local network builds
    │   ├── build-staging.sh      # Staging builds
    │   └── build-production.sh   # Production builds
    └── README-Environment.md     # Environment configuration guide
```

### Local Development
1. `pnpm install` - Install dependencies
2. Copy `.env.example` to `.env` in API project
3. `pnpm db:migrate` - Run database migrations
4. `pnpm db:seed` - Seed with sample data
5. `pnpm schema:update` - Generate GraphQL schema
6. `pnpm api:dev` - Start API server
7. **Mobile Development Options**:
   - `flutter run` - Default (localhost API)
   - `flutter run --dart-define=API_URL="http://************:3000/graphql"` - Custom API URL
   - `./scripts/build-dev.sh` - Development build
   - `./scripts/build-local.sh` - Local network build

### Environment Configuration
The mobile app supports flexible environment configuration:

#### **Available Environments**
| Environment | API URL | Build Script | Use Case |
|-------------|---------|--------------|----------|
| Development | `localhost:3000/graphql` | `build-dev.sh` | Local development |
| Local Network | `************:3000/graphql` | `build-local.sh` | Physical device testing |
| Staging | Template ready | `build-staging.sh` | QA testing |
| Production | Template ready | `build-production.sh` | Live deployment |

#### **Configuration Variables**
- **API_URL**: GraphQL endpoint URL (default: `http://localhost:3000/graphql`)
- **ENVIRONMENT**: Environment identifier (default: `development`)

## Technical Constraints & Performance
- **Performance**: App cold start < 2s, screen transitions < 100ms, 60fps animations, API P95 < 200ms
- **Security**: Row-Level Security for all user data, encrypted storage, secure token handling
- **Scalability**: Architecture supports 10,000 active users without major changes
- **Data Integrity**: Double-entry accounting principles, immutable transaction records

## Tool Usage Patterns

### Build & Code Quality Excellence (V2 Ruleset + Mobile App Compliance)
- **✅ Build Success**: `pnpm build` passes without errors (100% success)
- **✅ Zero Linting**: 0 errors/warnings with Biome.js (down from 35 errors + 117 warnings)
- **✅ Flutter Analysis Clean**: Zero warnings/infos - complete mobile code quality compliance
- **✅ V2 Ruleset Compliance**: 4/4 major subtasks COMPLETED - Full compliance achieved!
  - **Constants Centralization**: All magic strings/numbers in `constants.ts` with UPPER_SNAKE_CASE
  - **Function Length**: All functions under 25 lines with single responsibilities
  - **Type Safety**: Strategic use of `unknown` with controlled type assertions
  - **Modern Patterns**: Current Flutter API usage, proper code generation optimization
- **✅ Mobile App Code Quality**: 4/4 major subtasks COMPLETED - Excellence achieved!
  - **Widget Architecture**: Private widgets with single responsibilities and `const` constructors
  - **Immutable Data**: All models converted to `@freezed` with automatic code generation
  - **State Management**: Consistent `@freezed` state classes with generated methods
  - **Design Token Compliance**: Complete elimination of hardcoded colors (61+ instances fixed)
- **✅ Type Safety**: Eliminated ALL inappropriate `any`/`dynamic` types from entire codebase
- **✅ Modern Standards**: ESM modules, `node:` protocol imports, template literals

### Testing Framework Excellence
- **Perfect Success Rate**: **543/543 tests passing** across all test suites
- **Professional Organization**: Unit (344), Integration (178), E2E (41+) tests
- **Transaction-Based Isolation**: Perfect test isolation with automatic rollback
- **Dynamic Test Data**: Faker.js eliminates test conflicts and interference
- **CI/CD Ready**: Clean output, staged execution, comprehensive coverage

### Database & GraphQL
- **Drizzle ORM**: Type-safe database access with proper migrations and seeding
- **Code-First GraphQL**: API generates schema, mobile consumes via `schema:update`
- **Row-Level Security**: All user data protected with proper policies
- **Double-Entry Accounting**: Journal entries with corresponding journal lines

### Mobile Development Excellence

#### **Design System Integration**
- **Centralized Tokens**: `design_tokens.dart` as single source of truth for all design values
- **Theme Consistency**: `app_theme.dart` consumes all colors, spacing, typography from design tokens
- **Semantic Colors**: Proper usage of `onPrimary`, `onSecondary`, `onError` for accessibility
- **Component Theming**: AppBar, FloatingActionButton, and all components use semantic tokens

#### **Environment Management**
- **Flexible Configuration**: `--dart-define` approach for secure, compile-time configuration
- **Build Scripts**: Environment-specific builds for different deployment scenarios
- **Team Collaboration**: Different developers can use different local IP addresses
- **CI/CD Integration**: Build scripts work seamlessly with automated deployment
- **Zero Dependencies**: Uses Flutter's built-in configuration features
- **Security**: No sensitive configuration in source control

#### **Development Workflow**
- **Shell Routes**: Persistent navigation with go_router pattern
- **Material 3**: Consistent design system with centralized design tokens
- **Static Data**: Comprehensive financial examples ($68,345 net worth, category budgets)
- **Secure Storage**: flutter_secure_storage for sensitive authentication tokens
- **Debug Logging**: Environment information printed in debug mode for verification
