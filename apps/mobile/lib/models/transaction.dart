import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:budapp/models/account.dart';
import 'package:budapp/models/category.dart';

part 'transaction.freezed.dart';
part 'transaction.g.dart';

/// Helper for decoding enums from string values (for custom from<PERSON>son).
T _localEnumDecode<T>(Map<T, dynamic> enumMap, dynamic source) {
  return enumMap.entries
      .firstWhere((e) => e.value == source,
          orElse: () => throw ArgumentError(
              'Unknown enum value: $source for $T'))
      .key;
}

@freezed
class Transaction with _$Transaction {
  const factory Transaction({
    required String id,
    required String userId,
    String? description,
    required double amount,
    required TransactionType type,
    required DateTime date,
    String? notes,
    required TransactionStatus status,
    required bool isRecurring,
    RecurringPattern? recurringPattern,
    required DateTime createdAt,
    required DateTime updatedAt,
    required List<JournalLine> journalLines,
  }) = _Transaction;

  /// Custom fromJson to enforce non-null required fields and default required lists.
  /// If a required field is missing or null, throws a descriptive error.
  /// NOTE: The API must always return all required fields as non-null, including `journalLines: []` if empty.
  factory Transaction.fromJson(Map<String, dynamic> json) {
    // Defensive: Default journalLines to [] if missing/null, throw if still null after defaulting.
    final journalLinesRaw = json['journalLines'];
    final journalLines = (journalLinesRaw == null)
        ? <JournalLine>[]
        : (journalLinesRaw as List)
            .map((e) => JournalLine.fromJson(e as Map<String, dynamic>))
            .toList();

    // Check all required fields (except truly optional ones)
    final requiredFields = {
      'id': json['id'],
      'userId': json['userId'],
      'amount': json['amount'],
      'type': json['type'],
      'date': json['date'],
      'status': json['status'],
      'isRecurring': json['isRecurring'],
      'createdAt': json['createdAt'],
      'updatedAt': json['updatedAt'],
    };
    for (final entry in requiredFields.entries) {
      if (entry.value == null) {
        throw ArgumentError(
          "Transaction.fromJson: Required field '${entry.key}' is null or missing. "
          "API contract violation: all required fields must be non-null.",
        );
      }
    }

    return Transaction(
      id: json['id'] as String,
      userId: json['userId'] as String,
      description: json['description'] as String?,
      amount: (json['amount'] as num).toDouble(),
      type: TransactionTypeConverter.fromJson(json['type']),
      date: DateTime.parse(json['date'] as String),
      notes: json['notes'] as String?,
      status: _localEnumDecode(_$TransactionStatusEnumMap, json['status']),
      isRecurring: json['isRecurring'] as bool,
      recurringPattern: json['recurringPattern'] == null
          ? null
          : RecurringPattern.fromJson(
              json['recurringPattern'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      journalLines: journalLines,
    );
  }
}

@freezed
class JournalLine with _$JournalLine {
  const factory JournalLine({
    required String id,
    required String journalEntryId,
    required String accountId,
    String? categoryId,
    required double amount,
    required TransactionEntryType type,
    String? notes,
    required DateTime createdAt,
    required DateTime updatedAt,
    MiniAccount? account,
    Category? category,
  }) = _JournalLine;

  factory JournalLine.fromJson(Map<String, dynamic> json) =>
      _$JournalLineFromJson(json);
}

@freezed
class RecurringPattern with _$RecurringPattern {
  const factory RecurringPattern({
    required RecurringPatternType type,
    required int interval,
    int? dayOfWeek,
    int? dayOfMonth,
    int? monthOfYear,
    DateTime? endDate,
    int? occurrences,
  }) = _RecurringPattern;

  factory RecurringPattern.fromJson(Map<String, dynamic> json) =>
      _$RecurringPatternFromJson(json);
}

@freezed
class CreateTransactionInput with _$CreateTransactionInput {
  const factory CreateTransactionInput({
    required String description,
    required double amount,
    DateTime? date,
    String? notes,
    // For simple income/expense transactions
    String? accountId,
    String? categoryId,
    TransactionType? type,
    // For complex transactions or transfers
    List<JournalLineInput>? journalLines,
    // Recurring transaction fields
    @Default(false) bool isRecurring,
    RecurringPattern? recurringPattern,
  }) = _CreateTransactionInput;

  factory CreateTransactionInput.fromJson(Map<String, dynamic> json) =>
      _$CreateTransactionInputFromJson(json);
}

@freezed
class JournalLineInput with _$JournalLineInput {
  const factory JournalLineInput({
    required String accountId,
    String? categoryId,
    required double amount,
    required TransactionEntryType type,
    String? notes,
  }) = _JournalLineInput;

  factory JournalLineInput.fromJson(Map<String, dynamic> json) =>
      _$JournalLineInputFromJson(json);
}

@freezed
class UpdateTransactionInput with _$UpdateTransactionInput {
  const factory UpdateTransactionInput({
    String? description,
    DateTime? date,
    String? notes,
    TransactionStatus? status,
    bool? isRecurring,
    RecurringPattern? recurringPattern,
  }) = _UpdateTransactionInput;

  factory UpdateTransactionInput.fromJson(Map<String, dynamic> json) =>
      _$UpdateTransactionInputFromJson(json);
}

@freezed
class TransactionFilter with _$TransactionFilter {
  const factory TransactionFilter({
    String? accountId,
    String? categoryId,
    TransactionType? type,
    TransactionStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    double? minAmount,
    double? maxAmount,
    String? search,
    bool? isRecurring,
  }) = _TransactionFilter;

  factory TransactionFilter.fromJson(Map<String, dynamic> json) =>
      _$TransactionFilterFromJson(json);
}

enum TransactionType {
  @JsonValue('INCOME')
  income,
  @JsonValue('EXPENSE')
  expense,
  @JsonValue('TRANSFER')
  transfer,
}

/// Custom converter for TransactionType to handle lowercase API responses.
class TransactionTypeConverter {
  static TransactionType fromJson(dynamic value) {
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'income':
          return TransactionType.income;
        case 'expense':
          return TransactionType.expense;
        case 'transfer':
          return TransactionType.transfer;
        case 'INCOME':
          return TransactionType.income;
        case 'EXPENSE':
          return TransactionType.expense;
        case 'TRANSFER':
          return TransactionType.transfer;
      }
    }
    throw ArgumentError('Unknown TransactionType: $value');
  }

  static String toJson(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return 'INCOME';
      case TransactionType.expense:
        return 'EXPENSE';
      case TransactionType.transfer:
        return 'TRANSFER';
    }
  }
}

/// Helper for decoding enums from string values (for custom fromJson).
T _enumDecode<T>(Map<T, dynamic> enumMap, dynamic source) {
  return enumMap.entries
      .firstWhere((e) => e.value == source,
          orElse: () => throw ArgumentError(
              'Unknown enum value: $source for $T'))
      .key;
}

enum TransactionEntryType {
  @JsonValue('DEBIT')
  debit,
  @JsonValue('CREDIT')
  credit,
}

enum TransactionStatus {
  @JsonValue('PENDING')
  pending,
  @JsonValue('COMPLETED')
  completed,
  @JsonValue('CANCELLED')
  cancelled,
  @JsonValue('RECONCILED')
  reconciled,
}

enum RecurringPatternType {
  @JsonValue('DAILY')
  daily,
  @JsonValue('WEEKLY')
  weekly,
  @JsonValue('MONTHLY')
  monthly,
  @JsonValue('YEARLY')
  yearly,
}

// Extension methods for display
extension TransactionTypeExtension on TransactionType {
  String get displayName {
    switch (this) {
      case TransactionType.income:
        return 'Income';
      case TransactionType.expense:
        return 'Expense';
      case TransactionType.transfer:
        return 'Transfer';
    }
  }

  String get icon {
    switch (this) {
      case TransactionType.income:
        return '💰';
      case TransactionType.expense:
        return '💸';
      case TransactionType.transfer:
        return '🔄';
    }
  }
}

extension TransactionStatusExtension on TransactionStatus {
  String get displayName {
    switch (this) {
      case TransactionStatus.pending:
        return 'Pending';
      case TransactionStatus.completed:
        return 'Completed';
      case TransactionStatus.cancelled:
        return 'Cancelled';
      case TransactionStatus.reconciled:
        return 'Reconciled';
    }
  }
} 